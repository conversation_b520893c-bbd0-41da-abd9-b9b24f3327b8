import React, { useState, useEffect } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { ChevronDown, Menu, X } from 'lucide-react'

export default function Header() {
  const [findCareOpen, setFindCareOpen] = useState(false)
  const [careGroupsOpen, setCareGroupsOpen] = useState(false)
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const location = useLocation()

  // Close all dropdowns when route changes for proper Apple Mac desktop UX
  useEffect(() => {
    setFindCareOpen(false)
    setCareGroupsOpen(false)
    setMobileMenuOpen(false)
  }, [location.pathname])

  return (
    <header
      className="w-full px-8 py-4 flex justify-between items-center relative"
      style={{
        backgroundColor: 'var(--bg-primary)',
        borderBottom: '1px solid var(--border-light)',
        boxShadow: 'var(--shadow-light)'
      }}
    >
      {/* Logo */}
      <Link to="/" className="flex items-center no-underline gap-2.5">
        <div
          className="w-8 h-8 rounded-lg flex items-center justify-center text-white font-semibold text-sm tracking-tight"
          style={{ backgroundColor: 'var(--primary)' }}
        >
          CC
        </div>
        <Link
          to="/how-it-works"
          className="no-underline text-sm font-medium transition-all duration-200 px-3 py-2 rounded-md hover:bg-black/5"
          style={{ color: 'var(--text-primary)' }}
        >
          Care Connector
        </Link>
      </Link>

      {/* Mobile Menu Button */}
      <button
        onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
        className="md:hidden bg-transparent border-none p-2 rounded"
        aria-label="Toggle mobile menu"
        style={{ color: 'var(--text-secondary)' }}
      >
        {mobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
      </button>

      {/* Desktop Navigation */}
      <nav className="hidden md:flex items-center gap-6">
        {/* Find Care Dropdown */}
        <div className="relative group">
          <button
            onClick={() => setFindCareOpen(!findCareOpen)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault()
                setFindCareOpen(!findCareOpen)
              } else if (e.key === 'Escape' && findCareOpen) {
                setFindCareOpen(false)
              }
            }}
            aria-expanded={findCareOpen}
            aria-haspopup="true"
            aria-controls="find-care-menu"
            className="flex items-center gap-2.5 px-4 py-2 rounded-lg transition-all duration-150 ease-out hover:bg-gray-50/90 active:bg-gray-100/90 font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary/20"
          >
            <span className="font-medium" style={{ color: 'var(--text-primary)' }}>Find Care</span>
            <ChevronDown
              className={`w-4 h-4 transition-transform duration-200 ease-[cubic-bezier(0.23,1,0.32,1)] ${findCareOpen ? 'rotate-180' : ''}`}
              style={{ color: 'var(--text-primary)', opacity: 0.85 }}
            />
          </button>
          {findCareOpen && (
          <div
            id="find-care-menu"
            role="menu"
            aria-orientation="vertical"
            className={`absolute top-full left-0 mt-1 rounded-xl py-1.5 min-w-[220px] z-[100] transform transition-all duration-200 ease-[cubic-bezier(0.23,1,0.32,1)] ${findCareOpen ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-1 pointer-events-none'}`}
            style={{
              backgroundColor: 'var(--bg-primary)',
              boxShadow: '0 2px 6px rgba(0, 0, 0, 0.05)',
              border: '1px solid var(--border-light)',
              display: findCareOpen ? 'block' : 'none'
            }}
          >

              <Link
                to="/caregivers"
                onClick={() => setFindCareOpen(false)}
                role="menuitem"
                tabIndex={-1}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault()
                    setFindCareOpen(false)
                    window.location.href = '/caregivers'
                  }
                }}
                className={`block px-5 py-3 text-sm font-medium transition-all duration-150 hover:bg-gray-50/80 active:bg-gray-100/90 border-b border-gray-100/80 last:border-b-0 focus:outline-none focus:bg-gray-50/90 ${location.pathname === '/caregivers' ? 'font-semibold' : ''}`}
                style={{
                  color: location.pathname === '/caregivers' ? 'var(--primary)' : 'var(--text-secondary)'
                }}
              >
                Caregivers
              </Link>
              <div className="border-b border-gray-100/80 my-1" />
              <Link
                to="/companions"
                onClick={() => setFindCareOpen(false)}
                className={`dropdown-link block py-2.5 px-4 no-underline text-sm transition-all duration-200 hover:bg-black/5 ${
                  location.pathname === '/companions' ? 'font-semibold' : ''
                }`}
                style={{ 
                  color: location.pathname === '/companions' ? 'var(--primary)' : 'var(--text-secondary)'
                }}
              >
                Companions
              </Link>
              <div className="border-b border-gray-100/80 my-1" />
              <Link
                to="/professionals"
                onClick={() => setFindCareOpen(false)}
                className={`dropdown-link block py-2.5 px-4 no-underline text-sm transition-all duration-200 hover:bg-black/5 ${
                  location.pathname === '/professionals' ? 'font-semibold' : ''
                }`}
                style={{ 
                  color: location.pathname === '/professionals' ? 'var(--primary)' : 'var(--text-secondary)'
                }}
              >
                Professionals
              </Link>
              <div className="border-b border-gray-100/80 my-1" />
              <Link
                to="/care-checkers"
                onClick={() => setFindCareOpen(false)}
                className={`dropdown-link block py-2.5 px-4 no-underline text-sm transition-all duration-200 hover:bg-black/5 ${
                  location.pathname === '/care-checkers' ? 'font-semibold' : ''
                }`}
                style={{ 
                  color: location.pathname === '/care-checkers' ? 'var(--primary)' : 'var(--text-secondary)'
                }}
              >
                Care Checkers
              </Link>
            </div>
          )}
        </div>

        {/* Care Groups Dropdown */}
        <div className="relative">
          <button
            onClick={() => setCareGroupsOpen(!careGroupsOpen)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault()
                setCareGroupsOpen(!careGroupsOpen)
              } else if (e.key === 'Escape' && careGroupsOpen) {
                setCareGroupsOpen(false)
              }
            }}
            aria-expanded={careGroupsOpen}
            aria-haspopup="true"
            aria-controls="care-groups-menu"
            className="flex items-center gap-2.5 px-4 py-2 rounded-lg transition-all duration-150 ease-out hover:bg-gray-50/90 active:bg-gray-100/90 font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary/20"
            style={{ color: 'var(--text-primary)', opacity: 0.85 }}
          >
            Care Groups
            <ChevronDown className="w-4 h-4" />
          </button>

          {careGroupsOpen && (
            <div
              className={`absolute top-full left-0 mt-1 rounded-xl py-1.5 min-w-[220px] z-[100] transform transition-all duration-200 ease-[cubic-bezier(0.23,1,0.32,1)] ${careGroupsOpen ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-1 pointer-events-none'}`}
              style={{
                backgroundColor: 'var(--bg-primary)',
                boxShadow: '0 2px 6px rgba(0, 0, 0, 0.05)',
                border: '1px solid var(--border-light)'
              }}
          >
              <Link
                to="/browse-groups"
                onClick={() => setCareGroupsOpen(false)}
                className={`dropdown-link block py-2.5 px-4 no-underline text-sm transition-all duration-200 hover:bg-black/5 ${
                  location.pathname === '/browse-groups' ? 'font-semibold' : ''
                }`}
                style={{ 
                  color: location.pathname === '/browse-groups' ? 'var(--primary)' : 'var(--text-secondary)'
                }}
              >
                Browse Groups
              </Link>
              <Link
                to="/join-group"
                onClick={() => setCareGroupsOpen(false)}
                className={`dropdown-link block py-2.5 px-4 no-underline text-sm transition-all duration-200 hover:bg-black/5 ${
                  location.pathname === '/join-group' ? 'font-semibold' : ''
                }`}
                style={{ 
                  color: location.pathname === '/join-group' ? 'var(--primary)' : 'var(--text-secondary)'
                }}
              >
                Join a Group
              </Link>
            </div>
          )}
        </div>

        <Link
          to="/how-it-works"
          className={`no-underline font-medium text-sm transition-colors hover:opacity-80 ${
            location.pathname === '/how-it-works' ? 'font-semibold' : ''
          }`}
          style={{ 
            color: location.pathname === '/how-it-works' ? 'var(--primary)' : 'var(--text-secondary)'
          }}
        >
          How it Works
        </Link>
        <Link
          to="/features"
          className={`no-underline font-medium text-sm transition-colors hover:opacity-80 ${
            location.pathname === '/features' ? 'font-semibold' : ''
          }`}
          style={{
            color: location.pathname === '/features' ? 'var(--primary)' : 'var(--text-secondary)'
          }}
        >
          Features
        </Link>
        <Link
          to="/products"
          className={`no-underline font-medium text-sm transition-colors hover:opacity-80 ${
            location.pathname === '/products' ? 'font-semibold' : ''
          }`}
          style={{
            color: location.pathname === '/products' ? 'var(--primary)' : 'var(--text-secondary)'
          }}
        >
          Products
        </Link>
      </nav>

      {/* Desktop Auth Buttons */}
      <div className="flex items-center gap-4">
        <Link
          to="/sign-in"
          className="px-4 py-2 rounded-lg transition-all duration-200 hover:bg-gray-100 active:bg-gray-200 font-medium"
          style={{ color: 'var(--text-primary)' }}
        >
          Sign In
        </Link>
        <Link
          to="/get-started"
          className="px-4 py-2 rounded-lg bg-primary text-white transition-all duration-200 hover:bg-primary-dark hover:shadow-sm active:scale-[0.98] font-medium"
        >
          Get Started
        </Link>
      </div>

      {/* Mobile Menu Overlay */}
      {mobileMenuOpen && (
        <div
          className="md:hidden absolute top-full left-0 right-0 z-50"
          style={{
            backgroundColor: 'var(--bg-primary)',
            border: '1px solid var(--border-light)',
            boxShadow: 'var(--shadow-medium)'
          }}
        >
          <div className="p-4 space-y-4">
            {/* Mobile Find Care Section */}
            <div>
              <div className="font-medium text-sm mb-2" style={{ color: 'var(--text-primary)' }}>
                Find Care
              </div>
              <div className="pl-4 space-y-2">
                <Link
                  to="/find-care/companions"
                  onClick={() => setMobileMenuOpen(false)}
                  role="menuitem"
                  tabIndex={-1}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault()
                      setFindCareOpen(false)
                    }
                  }}
                  className="block px-5 py-3 text-sm font-medium transition-all duration-150 hover:bg-gray-50/80 active:bg-gray-100/90 border-b border-gray-100/80 last:border-b-0 focus:outline-none focus:bg-gray-50/90"
                  style={{ color: 'var(--text-primary)', opacity: 0.85 }}
                >
                  Companions
                </Link>
                <Link
                  to="/find-care/caregivers"
                  onClick={() => setMobileMenuOpen(false)}
                  role="menuitem"
                  tabIndex={-1}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault()
                      setFindCareOpen(false)
                    }
                  }}
                  className="block px-5 py-3 text-sm font-medium transition-all duration-150 hover:bg-gray-50/80 active:bg-gray-100/90 border-b border-gray-100/80 last:border-b-0 focus:outline-none focus:bg-gray-50/90"
                  style={{ color: 'var(--text-primary)', opacity: 0.85 }}
                >
                  Caregivers
                </Link>
                <Link
                  to="/find-care/nurses"
                  onClick={() => setMobileMenuOpen(false)}
                  role="menuitem"
                  tabIndex={-1}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault()
                      setFindCareOpen(false)
                    }
                  }}
                  className="block px-5 py-3 text-sm font-medium transition-all duration-150 hover:bg-gray-50/80 active:bg-gray-100/90 border-b border-gray-100/80 last:border-b-0 focus:outline-none focus:bg-gray-50/90"
                  style={{ color: 'var(--text-primary)', opacity: 0.85 }}
                >
                  Nurses
                </Link>
            </div>
          </div>

          {/* Mobile Care Groups Section */}
          <div>
            <div className="font-medium text-sm mb-2" style={{ color: 'var(--text-primary)' }}>
              Care Groups
            </div>
            <div className="pl-4 space-y-2">
              <Link
                to="/care-groups/elderly"
                onClick={() => setMobileMenuOpen(false)}
                className="block px-5 py-3 text-sm font-medium transition-all duration-150 hover:bg-gray-50/80 active:bg-gray-100/90 border-b border-gray-100/80 last:border-b-0 focus:outline-none focus:bg-gray-50/90"
                style={{ color: 'var(--text-primary)', opacity: 0.85 }}
              >
                Elderly
              </Link>
              <Link
                to="/care-groups/special-needs"
                onClick={() => setMobileMenuOpen(false)}
                className="block px-5 py-3 text-sm font-medium transition-all duration-150 hover:bg-gray-50/80 active:bg-gray-100/90 border-b border-gray-100/80 last:border-b-0 focus:outline-none focus:bg-gray-50/90"
                style={{ color: 'var(--text-primary)', opacity: 0.85 }}
              >
                Special Needs
              </Link>
              <Link
                to="/care-groups/children"
                onClick={() => setMobileMenuOpen(false)}
                className="block px-5 py-3 text-sm font-medium transition-all duration-150 hover:bg-gray-50/80 active:bg-gray-100/90 border-b border-gray-100/80 last:border-b-0 focus:outline-none focus:bg-gray-50/90"
                style={{ color: 'var(--text-primary)', opacity: 0.85 }}
              >
                Children
              </Link>
            </div>
          </div>

          {/* Mobile Other Links */}
          <div className="space-y-2">
            <Link
              to="/how-it-works"
              className="px-4 py-2 rounded-lg transition-all duration-200 hover:bg-gray-100 active:bg-gray-200 font-medium"
              style={{ color: 'var(--text-primary)', opacity: 0.85 }}
            >
              How It Works
            </Link>
            <Link
              to="/features"
              className="px-4 py-2 rounded-lg transition-all duration-200 hover:bg-gray-100 active:bg-gray-200 font-medium"
              style={{ color: 'var(--text-primary)', opacity: 0.85 }}
            >
              Features
            </Link>
            <Link
              to="/products"
              className="px-4 py-2 rounded-lg transition-all duration-200 hover:bg-gray-100 active:bg-gray-200 font-medium"
              style={{ color: 'var(--text-primary)', opacity: 0.85 }}
            >
              Products
            </Link>
          </div>

          {/* Mobile Auth Buttons */}
          <div className="pt-4 border-t space-y-3" style={{ borderColor: 'var(--border-light)' }}>
            <Link
              to="/sign-in"
              onClick={() => setMobileMenuOpen(false)}
              className="block py-2 text-sm font-medium"
              style={{ color: 'var(--text-secondary)' }}
            >
              Sign In
            </Link>
            <Link
              to="/sign-up"
              onClick={() => setMobileMenuOpen(false)}
              className="block py-2 text-sm font-medium"
              style={{ color: 'var(--text-secondary)' }}
            >
              Sign Up
            </Link>
          </div>
        </div>
      )}
    </header>
  )
}
                        window.location.href = '/care-groups/special-needs'
                      }
                    }}
                    className={`block px-5 py-3 text-sm font-medium transition-all duration-150 hover:bg-gray-50/80 active:bg-gray-100/90 border-b border-gray-100/80 last:border-b-0 focus:outline-none focus:bg-gray-50/90 ${location.pathname === '/care-groups/special-needs' ? 'font-semibold' : ''}`}
                    style={{ color: location.pathname === '/care-groups/special-needs' ? 'var(--primary)' : 'var(--text-secondary)' }}
                  >
                    Special Needs
                  </Link>
                  <Link
                    to="/care-groups/children"
                    onClick={() => setCareGroupsOpen(false)}
                    role="menuitem"
                    tabIndex={-1}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault()
                        setCareGroupsOpen(false)
                        window.location.href = '/care-groups/children'
                      }
                    }}
                    className={`block px-5 py-3 text-sm font-medium transition-all duration-150 hover:bg-gray-50/80 active:bg-gray-100/90 border-b border-gray-100/80 last:border-b-0 focus:outline-none focus:bg-gray-50/90 ${location.pathname === '/care-groups/children' ? 'font-semibold' : ''}`}
                    style={{ color: location.pathname === '/care-groups/children' ? 'var(--primary)' : 'var(--text-secondary)' }}
                  >
                    Children
                  </Link>
                </div>
              </div>
            )}
          </div>

          {/* Mobile Other Links */}
          <div className="space-y-2">
            <Link
              to="/how-it-works"
              className="px-4 py-2 rounded-lg transition-all duration-200 hover:bg-gray-100 active:bg-gray-200 font-medium"
              style={{ color: 'var(--text-primary)', opacity: 0.85 }}
            >
              How It Works
            </Link>
            <Link
              to="/features"
              className="px-4 py-2 rounded-lg transition-all duration-200 hover:bg-gray-100 active:bg-gray-200 font-medium"
              style={{ color: 'var(--text-primary)', opacity: 0.85 }}
            >
              Features
            </Link>
            <Link
              to="/products"
              className="px-4 py-2 rounded-lg transition-all duration-200 hover:bg-gray-100 active:bg-gray-200 font-medium"
              style={{ color: 'var(--text-primary)', opacity: 0.85 }}
            >
              Products
            </Link>
          </div>

          {/* Mobile Auth Buttons */}
          <div className="pt-4 border-t space-y-3" style={{ borderColor: 'var(--border-light)' }}>
            <Link
              to="/sign-in"
              onClick={() => setMobileMenuOpen(false)}
              className="block py-2 text-sm font-medium"
              style={{ color: 'var(--text-secondary)' }}
            >
              Sign In
            </Link>
            <Link
              to="/get-started"
              onClick={() => setMobileMenuOpen(false)}
              className="block py-3 px-4 rounded font-medium text-sm text-center"
              style={{ backgroundColor: 'var(--primary)', color: 'var(--bg-primary)' }}
            >
              Get Started
            </Link>
          </div>
        </div>
      </div>
      )}
    </header>
  )
}
