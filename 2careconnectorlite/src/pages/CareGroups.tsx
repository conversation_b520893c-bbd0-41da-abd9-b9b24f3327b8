import React, { useState, useEffect } from 'react'
import { dataService } from '../lib/dataService'
import { Search, Plus, Heart, X, Users, MapPin } from 'lucide-react'
import { supabase } from '../lib/supabase'

interface CareGroup {
  id: string
  name: string
  description?: string
  category?: string
  location?: string
  privacy_setting?: string
  member_count?: number
  created_at?: string
  avatar_url?: string
}

export default function CareGroups() {
  const [careGroups, setCareGroups] = useState<CareGroup[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [creating, setCreating] = useState(false)
  const [createError, setCreateError] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    category: '',
    location: '',
    privacy: 'public'
  })

  useEffect(() => {
    async function fetchCareGroups() {
      try {
        const data = await dataService.getCareGroups()
        setCareGroups(data)
      } catch (err) {
        console.error('Failed to load care groups:', err)
      } finally {
        setLoading(false)
      }
    }
    fetchCareGroups()
  }, [])

  const handleCreateGroup = async (e: React.FormEvent) => {
    e.preventDefault()
    setCreating(true)
    setCreateError('')

    try {
      // Get current user
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        setCreateError('You must be signed in to create a care group')
        return
      }

      // Create care group in database
      const { data, error } = await supabase
        .from('care_groups')
        .insert({
          name: formData.name,
          description: formData.description,
          category: formData.category,
          location: formData.location,
          privacy: formData.privacy,
          created_by: session.user.id,
          member_count: 1
        })
        .select()
        .single()

      if (error) throw error

      // Add creator as first member
      await supabase
        .from('care_group_members')
        .insert({
          group_id: data.id,
          user_id: session.user.id,
          role: 'admin',
          joined_at: new Date().toISOString()
        })

      // Refresh care groups list
      const updatedGroups = await dataService.getCareGroups()
      setCareGroups(updatedGroups)

      // Close modal and reset form
      setShowCreateModal(false)
      setFormData({
        name: '',
        description: '',
        category: '',
        location: '',
        privacy: 'public'
      })

      console.log('Care group created successfully:', data)
    } catch (error: any) {
      console.error('Error creating care group:', error)
      setCreateError(error.message || 'Failed to create care group')
    } finally {
      setCreating(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  if (loading) return <div style={{ padding: '2rem', textAlign: 'center' }}>Loading care groups...</div>

  return (
    <div style={{ padding: '2rem' }}>
      <h1 style={{ 
        fontSize: '2rem', 
        fontWeight: '700', 
        color: 'var(--text-primary)', 
        marginBottom: '1rem',
        textAlign: 'center' 
      }}>
        Browse Care Groups
      </h1>
      
      <p style={{ 
        textAlign: 'center', 
        color: 'var(--text-secondary)', 
        marginBottom: '2rem',
        maxWidth: '600px',
        margin: '0 auto 2rem auto'
      }}>
        Discover and join care groups in your community. Connect with others who share similar care journeys and experiences.
      </p>

      {/* Create Group Button */}
      <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
        <button 
          onClick={() => setShowCreateModal(true)}
          style={{
            backgroundColor: 'var(--primary)',
            color: 'white',
            padding: '0.75rem 1.5rem',
            borderRadius: '0.375rem',
            border: 'none',
            fontWeight: '500',
            fontSize: '0.875rem',
            cursor: 'pointer',
            display: 'inline-flex',
            alignItems: 'center',
            gap: '0.5rem',
            transition: 'background-color 0.2s'
          }}
          onMouseOver={(e) => e.currentTarget.style.backgroundColor = 'var(--primary-dark)'}
          onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'var(--primary)'}
        >
          <Plus style={{ width: '1rem', height: '1rem' }} />
          Create Your Care Group
        </button>
      </div>

      {/* Search Bar */}
      <div style={{
        maxWidth: '500px',
        margin: '0 auto 3rem auto',
        position: 'relative'
      }}>
        <Search style={{
          position: 'absolute',
          left: '1rem',
          top: '50%',
          transform: 'translateY(-50%)',
          width: '1.25rem',
          height: '1.25rem',
          color: 'var(--text-secondary)'
        }} />
        <input
          type="text"
          placeholder="Search support groups by name or condition..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          style={{
            width: '100%',
            padding: '0.75rem 1rem 0.75rem 3rem',
            borderRadius: '0.5rem',
            border: '1px solid var(--border-medium)',
            fontSize: '1rem',
            backgroundColor: 'var(--bg-primary)'
          }}
        />
      </div>

      {/* Filter care groups based on search query */}
      {(() => {
        const filteredGroups = careGroups.filter(group =>
          group.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          (group.description && group.description.toLowerCase().includes(searchQuery.toLowerCase())) ||
          (group.category && group.category.toLowerCase().includes(searchQuery.toLowerCase()))
        )

        return filteredGroups.length === 0 ? (
        <div style={{ 
          textAlign: 'center', 
          padding: '4rem 2rem', 
          backgroundColor: 'var(--bg-secondary)', 
          borderRadius: '0.5rem',
          border: '1px solid var(--border-light)'
        }}>
          <Heart style={{
            width: '4rem',
            height: '4rem',
            color: 'var(--text-muted)',
            margin: '0 auto 1.5rem auto'
          }} />
          
          <h3 style={{
            fontSize: '1.25rem',
            fontWeight: '600',
            color: 'var(--text-primary)',
            marginBottom: '0.5rem'
          }}>
            No care groups available
          </h3>
          
          <p style={{ 
            color: 'var(--text-secondary)',
            marginBottom: '2rem'
          }}>
            Be the first to create a care group in your community.
          </p>

          <button 
            onClick={() => setShowCreateModal(true)}
            style={{
              backgroundColor: 'var(--primary)',
              color: 'white',
              padding: '0.75rem 1.5rem',
              borderRadius: '0.375rem',
              border: 'none',
              fontWeight: '500',
              fontSize: '0.875rem',
              cursor: 'pointer',
              display: 'inline-flex',
              alignItems: 'center',
              gap: '0.5rem',
              transition: 'background-color 0.2s'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = 'var(--primary-dark)'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'var(--primary)'}
          >
            <Plus style={{ width: '1rem', height: '1rem' }} />
            Create First Care Group
          </button>
        </div>
      ) : (
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))', 
          gap: '2rem',
          maxWidth: '1200px',
          margin: '0 auto'
        }}>
          {filteredGroups.map((group: any) => (
            <div key={group.id} style={{ 
              backgroundColor: 'var(--bg-primary)', 
              padding: '2rem', 
              borderRadius: '0.5rem', 
              border: '1px solid var(--border-light)',
              textAlign: 'center'
            }}>
              <h3 style={{ 
                color: 'var(--text-primary)', 
                marginBottom: '1rem',
                fontSize: '1.25rem',
                fontWeight: '600'
              }}>
                {group.name}
              </h3>
              
              {group.description && (
                <p style={{ 
                  color: 'var(--text-secondary)',
                  marginBottom: '1.5rem',
                  lineHeight: '1.4'
                }}>
                  {group.description}
                </p>
              )}

              <button
                onClick={() => alert(`Joining ${group.name}...`)}
                onMouseEnter={(e) => e.currentTarget.style.opacity = '0.9'}
                onMouseLeave={(e) => e.currentTarget.style.opacity = '1'}
                style={{
                  backgroundColor: 'var(--primary)',
                  color: 'white',
                  padding: '0.75rem 1.5rem',
                  borderRadius: '0.375rem',
                  border: 'none',
                  fontWeight: '500',
                  fontSize: '0.875rem',
                  cursor: 'pointer',
                  width: '100%',
                  transition: 'opacity 0.2s'
                }}>
                Join Group
              </button>
            </div>
          ))}
        </div>
        )
      })()}

      {/* Create Care Group Modal */}
      {showCreateModal && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'var(--overlay-dark)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000,
          padding: '1rem'
        }}>
          <div style={{
            backgroundColor: 'var(--bg-primary)',
            borderRadius: '0.5rem',
            padding: '2rem',
            width: '100%',
            maxWidth: '500px',
            maxHeight: '90vh',
            overflowY: 'auto',
            position: 'relative'
          }}>
            {/* Modal Header */}
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '1.5rem'
            }}>
              <h2 style={{
                fontSize: '1.5rem',
                fontWeight: '600',
                color: 'var(--text-primary)',
                margin: 0
              }}>
                Create Care Group
              </h2>
              <button
                onClick={() => {
                  setShowCreateModal(false)
                  setCreateError('')
                  setFormData({
                    name: '',
                    description: '',
                    category: '',
                    location: '',
                    privacy: 'public'
                  })
                }}
                style={{
                  background: 'none',
                  border: 'none',
                  padding: '0.5rem',
                  cursor: 'pointer',
                  borderRadius: '0.25rem',
                  color: 'var(--text-secondary)'
                }}
              >
                <X style={{ width: '1.25rem', height: '1.25rem' }} />
              </button>
            </div>

            {/* Error Display */}
            {createError && (
              <div style={{
                backgroundColor: 'var(--bg-error)',
                border: '1px solid var(--border-error)',
                color: 'var(--text-error)',
                padding: '0.75rem',
                borderRadius: '0.375rem',
                marginBottom: '1.5rem',
                fontSize: '0.875rem'
              }}>
                {createError}
              </div>
            )}

            {/* Form */}
            <form onSubmit={handleCreateGroup}>
              {/* Group Name */}
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: 'var(--text-primary)',
                  marginBottom: '0.5rem'
                }}>
                  Group Name *
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                  placeholder="Enter group name"
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    borderRadius: '0.375rem',
                    border: '1px solid var(--border-medium)',
                    fontSize: '1rem',
                    backgroundColor: 'var(--bg-primary)'
                  }}
                />
              </div>

              {/* Description */}
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: 'var(--text-primary)',
                  marginBottom: '0.5rem'
                }}>
                  Description
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={3}
                  placeholder="Describe your care group's purpose and goals"
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    borderRadius: '0.375rem',
                    border: '1px solid var(--border-medium)',
                    fontSize: '1rem',
                    backgroundColor: 'var(--bg-primary)',
                    resize: 'vertical'
                  }}
                />
              </div>

              {/* Category */}
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: 'var(--text-primary)',
                  marginBottom: '0.5rem'
                }}>
                  Category
                </label>
                <select
                  name="category"
                  value={formData.category}
                  onChange={handleInputChange}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    borderRadius: '0.375rem',
                    border: '1px solid var(--border-medium)',
                    fontSize: '1rem',
                    backgroundColor: 'var(--bg-primary)'
                  }}
                >
                  <option value="">Select a category</option>
                  <option value="alzheimers">Alzheimer's & Dementia</option>
                  <option value="cancer">Cancer Support</option>
                  <option value="chronic-illness">Chronic Illness</option>
                  <option value="mental-health">Mental Health</option>
                  <option value="senior-care">Senior Care</option>
                  <option value="disability">Disability Support</option>
                  <option value="family-caregivers">Family Caregivers</option>
                  <option value="other">Other</option>
                </select>
              </div>

              {/* Location */}
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: 'var(--text-primary)',
                  marginBottom: '0.5rem'
                }}>
                  <MapPin style={{ width: '1rem', height: '1rem', display: 'inline', marginRight: '0.25rem' }} />
                  Location
                </label>
                <input
                  type="text"
                  name="location"
                  value={formData.location}
                  onChange={handleInputChange}
                  placeholder="City, State or Online"
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    borderRadius: '0.375rem',
                    border: '1px solid var(--border-medium)',
                    fontSize: '1rem',
                    backgroundColor: 'var(--bg-primary)'
                  }}
                />
              </div>

              {/* Privacy Setting */}
              <div style={{ marginBottom: '2rem' }}>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: 'var(--text-primary)',
                  marginBottom: '0.5rem'
                }}>
                  Privacy Setting
                </label>
                <select
                  name="privacy"
                  value={formData.privacy}
                  onChange={handleInputChange}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    borderRadius: '0.375rem',
                    border: '1px solid var(--border-medium)',
                    fontSize: '1rem',
                    backgroundColor: 'var(--bg-primary)'
                  }}
                >
                  <option value="public">Public - Anyone can find and join</option>
                  <option value="private">Private - Invitation only</option>
                </select>
              </div>

              {/* Action Buttons */}
              <div style={{
                display: 'flex',
                gap: '1rem',
                justifyContent: 'flex-end'
              }}>
                <button
                  type="button"
                  onClick={() => {
                    setShowCreateModal(false)
                    setCreateError('')
                    setFormData({
                      name: '',
                      description: '',
                      category: '',
                      location: '',
                      privacy: 'public'
                    })
                  }}
                  style={{
                    padding: '0.75rem 1.5rem',
                    borderRadius: '0.375rem',
                    border: '1px solid var(--border-medium)',
                    backgroundColor: 'var(--bg-primary)',
                    color: 'var(--text-secondary)',
                    fontWeight: '500',
                    fontSize: '0.875rem',
                    cursor: 'pointer'
                  }}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={creating || !formData.name.trim()}
                  style={{
                    padding: '0.75rem 1.5rem',
                    borderRadius: '0.375rem',
                    border: 'none',
                    backgroundColor: creating || !formData.name.trim() ? 'var(--text-muted)' : 'var(--primary)',
                    color: 'white',
                    fontWeight: '500',
                    fontSize: '0.875rem',
                    cursor: creating || !formData.name.trim() ? 'not-allowed' : 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem'
                  }}
                >
                  {creating ? (
                    <>
                      <div style={{
                        width: '1rem',
                        height: '1rem',
                        border: '2px solid transparent',
                        borderTop: '2px solid white',
                        borderRadius: '50%',
                        animation: 'spin 1s linear infinite'
                      }} />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Users style={{ width: '1rem', height: '1rem' }} />
                      Create Group
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}
