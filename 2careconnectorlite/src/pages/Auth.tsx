import React, { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { Mail, Lock, Eye, EyeOff } from 'lucide-react'
import { supabase } from '../lib/supabase'

export default function Auth() {
  const [isSignIn, setIsSignIn] = useState(true)
  const [showPassword, setShowPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: ''
  })
  const navigate = useNavigate()

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      if (isSignIn) {
        // Sign In Logic
        const { data, error } = await supabase.auth.signInWithPassword({
          email: formData.email,
          password: formData.password,
        })

        if (error) throw error

        if (data.user) {
          console.log('Sign in successful:', data.user)
          // Navigate to dashboard after successful sign-in
          navigate('/dashboard')
        }
      } else {
        // Sign Up Logic
        if (formData.password !== formData.confirmPassword) {
          throw new Error('Passwords do not match')
        }

        const { data, error } = await supabase.auth.signUp({
          email: formData.email,
          password: formData.password,
          options: {
            data: {
              first_name: formData.firstName,
              last_name: formData.lastName,
            },
          },
        })

        if (error) throw error

        if (data.user) {
          console.log('Sign up successful:', data.user)
          // Navigate to dashboard after successful sign-up
          navigate('/dashboard')
        }
      }
    } catch (error: any) {
      console.error('Authentication error:', error)
      setError(error.message || 'An error occurred during authentication')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex flex-col justify-center py-8 sm:px-6 lg:px-8" style={{ backgroundColor: 'var(--bg-secondary)' }}>
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <Link to="/" className="flex justify-center">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 rounded-md flex items-center justify-center" style={{ backgroundColor: 'var(--primary)' }}>
              <span className="text-white font-bold text-sm">CC</span>
            </div>
            <span className="text-xl font-semibold" style={{ color: 'var(--text-primary)' }}>Care Connector</span>
          </div>
        </Link>
        <h2 className="mt-4 text-center text-2xl font-semibold" style={{ color: 'var(--text-primary)' }}>
          {isSignIn ? 'Welcome back to your care network' : 'Join our trusted care community'}
        </h2>
        <p className="mt-2 text-center text-sm" style={{ color: 'var(--text-secondary)' }}>
          {isSignIn
            ? "Access your personalized healthcare coordination platform"
            : "Create your secure account to connect with verified care professionals"
          }
        </p>
        <p className="mt-3 text-center text-sm" style={{ color: 'var(--text-secondary)' }}>
          {isSignIn ? "Don't have an account? " : "Already have an account? "}
          <button
            onClick={() => setIsSignIn(!isSignIn)}
            className="font-medium transition-colors"
            style={{ color: 'var(--primary)' }}
            onMouseEnter={(e) => e.currentTarget.style.opacity = '0.8'}
            onMouseLeave={(e) => e.currentTarget.style.opacity = '1'}
          >
            {isSignIn ? 'Create account' : 'Sign in'}
          </button>
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="py-8 px-4 shadow sm:rounded-lg sm:px-10" style={{ backgroundColor: 'var(--bg-primary)' }}>
          {error && (
            <div className="mb-4 p-3 rounded-md border" style={{
              backgroundColor: 'var(--bg-error)',
              borderColor: 'var(--border-error)'
            }}>
              <p className="text-sm" style={{ color: 'var(--text-error)' }}>{error}</p>
            </div>
          )}
          <form onSubmit={handleSubmit} className="space-y-6">
            {!isSignIn && (
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label htmlFor="firstName" className="block text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                    First name
                  </label>
                  <div className="mt-1">
                    <input
                      id="firstName"
                      name="firstName"
                      type="text"
                      required={!isSignIn}
                      value={formData.firstName}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 focus:outline-none"
                      style={{
                        borderColor: 'var(--border-medium)',
                        color: 'var(--text-primary)'
                      }}
                      onFocus={(e) => {
                        e.target.style.borderColor = 'var(--primary)'
                        e.target.style.boxShadow = `0 0 0 3px var(--focus-shadow)`
                      }}
                      onBlur={(e) => {
                        e.target.style.borderColor = 'var(--border-medium)'
                        e.target.style.boxShadow = 'none'
                      }}
                      placeholder="First name"
                    />
                  </div>
                </div>
                <div>
                  <label htmlFor="lastName" className="block text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                    Last name
                  </label>
                  <div className="mt-1">
                    <input
                      id="lastName"
                      name="lastName"
                      type="text"
                      required={!isSignIn}
                      value={formData.lastName}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none"
                      style={{
                        borderColor: 'var(--border-medium)',
                        color: 'var(--text-primary)'
                      }}
                      onFocus={(e) => {
                        e.target.style.borderColor = 'var(--primary)'
                        e.target.style.boxShadow = `0 0 0 3px var(--focus-shadow)`
                      }}
                      onBlur={(e) => {
                        e.target.style.borderColor = 'var(--border-medium)'
                        e.target.style.boxShadow = 'none'
                      }}
                      placeholder="Last name"
                    />
                  </div>
                </div>
              </div>
            )}

            <div>
              <label htmlFor="email" className="block text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                Email address
              </label>
              <div className="mt-1 relative">
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full pl-10 pr-3 py-2 border rounded-md shadow-sm focus:outline-none"
                  style={{
                    borderColor: 'var(--border-medium)',
                    color: 'var(--text-primary)'
                  }}
                  onFocus={(e) => {
                    e.target.style.borderColor = 'var(--primary)'
                    e.target.style.boxShadow = `0 0 0 3px var(--focus-shadow)`
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = 'var(--border-medium)'
                    e.target.style.boxShadow = 'none'
                  }}
                  placeholder="<EMAIL>"
                />
                <Mail className="w-5 h-5 absolute left-3 top-2.5" style={{ color: 'var(--text-muted)' }} />
              </div>
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                Password
              </label>
              <div className="mt-1 relative">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete={isSignIn ? 'current-password' : 'new-password'}
                  required
                  value={formData.password}
                  onChange={handleInputChange}
                  className="w-full pl-10 pr-10 py-2 border rounded-md shadow-sm focus:outline-none"
                  style={{
                    borderColor: 'var(--border-medium)',
                    color: 'var(--text-primary)'
                  }}
                  onFocus={(e) => {
                    e.target.style.borderColor = 'var(--primary)'
                    e.target.style.boxShadow = `0 0 0 3px var(--focus-shadow)`
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = 'var(--border-medium)'
                    e.target.style.boxShadow = 'none'
                  }}
                  placeholder="Create a secure password"
                />
                <Lock className="w-5 h-5 absolute left-3 top-2.5" style={{ color: 'var(--text-muted)' }} />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-2.5 transition-colors"
                  style={{ color: 'var(--text-muted)' }}
                  onMouseEnter={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
                  onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-muted)'}
                >
                  {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
            </div>

            {!isSignIn && (
              <div>
                <label htmlFor="confirmPassword" className="block text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                  Confirm password
                </label>
                <div className="mt-1 relative">
                  <input
                    id="confirmPassword"
                    name="confirmPassword"
                    type={showPassword ? 'text' : 'password'}
                    autoComplete="new-password"
                    required={!isSignIn}
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    className="w-full pl-10 pr-3 py-2 border rounded-md shadow-sm focus:outline-none"
                    style={{
                      borderColor: 'var(--border-medium)',
                      color: 'var(--text-primary)'
                    }}
                    onFocus={(e) => {
                      e.target.style.borderColor = 'var(--primary)'
                      e.target.style.boxShadow = `0 0 0 3px var(--focus-shadow)`
                    }}
                    onBlur={(e) => {
                      e.target.style.borderColor = 'var(--border-medium)'
                      e.target.style.boxShadow = 'none'
                    }}
                    placeholder="Re-enter your password"
                  />
                  <Lock className="w-5 h-5 absolute left-3 top-2.5" style={{ color: 'var(--text-muted)' }} />
                </div>
              </div>
            )}

            {!isSignIn && (
              <div className="space-y-4">
                <div className="flex items-start">
                  <input
                    id="terms-acceptance"
                    name="terms-acceptance"
                    type="checkbox"
                    required
                    className="h-4 w-4 rounded mt-1 focus:outline-none"
                    style={{
                      accentColor: 'var(--primary)',
                      borderColor: 'var(--border-medium)'
                    }}
                  />
                  <label htmlFor="terms-acceptance" className="ml-3 block text-sm leading-5" style={{ color: 'var(--text-secondary)' }}>
                    I agree to the{' '}
                    <a href="/terms-of-service" className="font-medium transition-colors" style={{ color: 'var(--primary)' }}
                       onMouseEnter={(e) => e.currentTarget.style.opacity = '0.8'}
                       onMouseLeave={(e) => e.currentTarget.style.opacity = '1'}>
                      Terms of Service
                    </a>{' '}
                    and{' '}
                    <a href="/privacy-policy" className="font-medium transition-colors" style={{ color: 'var(--primary)' }}
                       onMouseEnter={(e) => e.currentTarget.style.opacity = '0.8'}
                       onMouseLeave={(e) => e.currentTarget.style.opacity = '1'}>
                      Privacy Policy
                    </a>
                  </label>
                </div>
                <div className="flex items-start">
                  <input
                    id="hipaa-acknowledgment"
                    name="hipaa-acknowledgment"
                    type="checkbox"
                    required
                    className="h-4 w-4 rounded mt-1 focus:outline-none"
                    style={{
                      accentColor: 'var(--primary)',
                      borderColor: 'var(--border-medium)'
                    }}
                  />
                  <label htmlFor="hipaa-acknowledgment" className="ml-3 block text-sm leading-5" style={{ color: 'var(--text-secondary)' }}>
                    I acknowledge receipt of the{' '}
                    <a href="/hipaa-notice" className="font-medium transition-colors" style={{ color: 'var(--primary)' }}
                       onMouseEnter={(e) => e.currentTarget.style.opacity = '0.8'}
                       onMouseLeave={(e) => e.currentTarget.style.opacity = '1'}>
                      HIPAA Notice of Privacy Practices
                    </a>{' '}
                    and understand how my health information may be used and disclosed
                  </label>
                </div>
              </div>
            )}

            {isSignIn && (
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <input
                    id="remember-me"
                    name="remember-me"
                    type="checkbox"
                    className="h-4 w-4 rounded focus:outline-none"
                    style={{
                      accentColor: 'var(--primary)',
                      borderColor: 'var(--border-medium)'
                    }}
                  />
                  <label htmlFor="remember-me" className="ml-2 block text-sm" style={{ color: 'var(--text-primary)' }}>
                    Remember me
                  </label>
                </div>

                <div className="text-sm">
                  <a href="#" className="font-medium transition-colors" style={{ color: 'var(--primary)' }}
                     onMouseEnter={(e) => e.currentTarget.style.opacity = '0.8'}
                     onMouseLeave={(e) => e.currentTarget.style.opacity = '1'}>
                    Forgot your password?
                  </a>
                </div>
              </div>
            )}

            <div>
              <button
                type="submit"
                disabled={loading}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                style={{
                  backgroundColor: 'var(--primary)',
                  color: 'var(--bg-primary)'
                }}
                onMouseEnter={(e) => !loading && (e.currentTarget.style.opacity = '0.9')}
                onMouseLeave={(e) => !loading && (e.currentTarget.style.opacity = '1')}
                onFocus={(e) => {
                  e.currentTarget.style.boxShadow = `0 0 0 3px var(--focus-shadow)`
                }}
                onBlur={(e) => {
                  e.currentTarget.style.boxShadow = 'none'
                }}
              >
                {loading ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-3"></div>
                    <div className="flex flex-col items-start">
                      <span className="text-sm font-medium">
                        {isSignIn ? 'Authenticating...' : 'Creating your secure account...'}
                      </span>
                      <span className="text-xs opacity-80 mt-1">
                        {isSignIn ? 'Verifying credentials' : 'Setting up your care profile'}
                      </span>
                    </div>
                  </div>
                ) : (
                  isSignIn ? 'Sign in to your care network' : 'Create your care account'
                )}
              </button>
            </div>
          </form>

          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t" style={{ borderColor: 'var(--border-light)' }} />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 text-sm" style={{ backgroundColor: 'var(--bg-primary)', color: 'var(--text-secondary)' }}>Or continue with</span>
              </div>
            </div>

            <div className="mt-6 grid grid-cols-2 gap-3">
              <button
                type="button"
                className="w-full inline-flex justify-center py-2 px-4 border rounded-md shadow-sm text-sm font-medium transition-colors"
                style={{
                  backgroundColor: 'var(--bg-primary)',
                  borderColor: 'var(--border-medium)',
                  color: 'var(--text-secondary)'
                }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-primary)'}
              >
                <svg className="w-5 h-5" viewBox="0 0 24 24">
                  <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                  <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                  <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                  <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                <span className="ml-2">Google</span>
              </button>

              <button
                type="button"
                className="w-full inline-flex justify-center py-2 px-4 border rounded-md shadow-sm text-sm font-medium transition-colors"
                style={{
                  backgroundColor: 'var(--bg-primary)',
                  borderColor: 'var(--border-medium)',
                  color: 'var(--text-secondary)'
                }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-primary)'}
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
                <span className="ml-2">Facebook</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
