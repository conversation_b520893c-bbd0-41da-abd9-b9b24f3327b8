import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { useNavigate } from 'react-router-dom'
import { supabase } from '../lib/supabase'
import { User, Calendar, MessageSquare, Heart, Bell, Settings } from 'lucide-react'

interface UserProfile {
  id: string
  email: string
  first_name?: string
  last_name?: string
  full_name?: string
  avatar_url?: string
  role?: string
  created_at?: string
}

export default function Dashboard() {
  const navigate = useNavigate()
  const [user, setUser] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [activityFilter, setActivityFilter] = useState('all')
  const [dashboardStats, setDashboardStats] = useState({
    upcomingAppointments: 0,
    unreadMessages: 0,
    careGroupsCount: 0,
    savedProviders: 0,
    completedAppointments: 0,
    activeConversations: 0,
    healthGoalsProgress: 0,
    medicationReminders: 0
  })

  // Memoized dashboard stats for performance optimization
  const memoizedStats = useMemo(() => ({
    appointmentProgress: Math.min((dashboardStats.upcomingAppointments / 10) * 100, 100),
    messageProgress: Math.min((dashboardStats.unreadMessages / 20) * 100, 100),
    messagePriority: dashboardStats.unreadMessages > 5 ? 'high' : 'normal',
    careGroupProgress: Math.min((dashboardStats.careGroupsCount / 50) * 100, 100),
    healthProgress: Math.min((dashboardStats.healthGoalsProgress / 100) * 100, 100)
  }), [dashboardStats])

  // Optimized tab switching with useCallback
  const handleTabSwitch = useCallback((tab: string) => {
    setActiveTab(tab)
  }, [])

  // Optimized search handler
  const handleSearchChange = useCallback((query: string) => {
    setSearchQuery(query)
  }, [])

  // Optimized filter handler
  const handleFilterChange = useCallback((filter: string) => {
    setActivityFilter(filter)
  }, [])

  useEffect(() => {
    checkUser()
  }, [])

  const checkUser = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      
      console.log('Dashboard checkUser - session:', session)
      
      if (!session?.user) {
        console.log('No session found, redirecting to auth')
        navigate('/auth')
        return
      }

      console.log('Session found, fetching profile for user:', session.user.id)
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', session.user.id)
        .single()
      
      console.log('Profile query result:', { profile, profileError })

      if (profile) {
        setUser({
          id: profile.id,
          email: profile.email || session.user.email || '',
          first_name: profile.first_name,
          last_name: profile.last_name,
          full_name: profile.full_name,
          avatar_url: profile.avatar_url,
          role: profile.role,
          created_at: profile.created_at
        })
        
        // Load dashboard stats
        await loadDashboardStats(session.user.id)
      }
    } catch (error) {
      console.error('Error checking user:', error)
      setError('Failed to load user data')
    } finally {
      setLoading(false)
    }
  }

  const loadDashboardStats = async (userId: string) => {
    try {
      // Get upcoming appointments count
      const { data: appointments } = await supabase
        .from('bookings')
        .select('id')
        .eq('user_id', userId)
        .eq('status', 'confirmed')
        .gte('start_time', new Date().toISOString())

      // Get unread messages count
      const { data: messagesData } = await supabase
        .from('messages')
        .select('id')
        .eq('recipient_id', userId)
        .eq('is_read', false)

      const unreadMessages = messagesData?.length || 0

      // Get saved providers count
      const { data: savedProvidersData } = await supabase
        .from('saved_providers')
        .select('id')
        .eq('user_id', userId)

      const savedProviders = savedProvidersData?.length || 0

      // Get care groups count
      const { data: careGroups } = await supabase
        .from('care_group_members')
        .select('care_group_id')
        .eq('user_id', userId)

      setDashboardStats({
        upcomingAppointments: appointments?.length || 0,
        unreadMessages,
        careGroupsCount: careGroups?.length || 0,
        savedProviders,
        completedAppointments: 0,
        activeConversations: 0,
        healthGoalsProgress: 0,
        medicationReminders: 0
      })
    } catch (error) {
      console.error('Error loading dashboard stats:', error)
      setError('Failed to load dashboard statistics')
    }
  }

  const handleSignOut = async () => {
    try {
      await supabase.auth.signOut()
      navigate('/')
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: 'var(--bg-secondary)' }}>
        <div className="text-center">
          <div className="flex justify-center mb-6">
            <div className="relative">
              <div className="w-16 h-16 rounded-full border-3 border-transparent animate-spin"
                   style={{ borderTopColor: 'var(--primary)', borderRightColor: 'var(--primary)' }}></div>
              <div className="absolute inset-0 w-16 h-16 rounded-full border-3 opacity-20"
                   style={{ borderColor: 'var(--primary)' }}></div>
            </div>
          </div>
          <h2 className="text-2xl font-bold mb-4 tracking-tight" style={{ color: 'var(--text-primary)' }}>Loading Dashboard</h2>
          <p className="text-lg" style={{ color: 'var(--text-secondary)' }}>Please wait while we load your data...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: 'var(--bg-secondary)' }}>
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center" style={{ backgroundColor: 'var(--bg-error)' }}>
            <svg className="w-8 h-8" style={{ color: 'var(--text-error)' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold mb-4 tracking-tight" style={{ color: 'var(--text-primary)' }}>Unable to Load Dashboard</h2>
          <p className="text-lg mb-6" style={{ color: 'var(--text-secondary)' }}>{error}</p>
          <div className="space-y-3">
            <button
              onClick={() => window.location.reload()}
              className="w-full px-6 py-3 rounded-lg font-semibold transition-colors"
              style={{ backgroundColor: 'var(--primary)', color: 'var(--bg-primary)' }}
            >
              Retry
            </button>
            <button
              onClick={handleSignOut}
              className="w-full px-6 py-3 rounded-lg font-semibold transition-colors border"
              style={{ borderColor: 'var(--border-medium)', color: 'var(--text-primary)' }}
            >
              Sign Out
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex" style={{ backgroundColor: 'var(--bg-secondary)' }}>
      {/* Apple Mac Desktop Style Sidebar */}
      <div className="w-64 flex-shrink-0 border-r"
           style={{
             backgroundColor: 'var(--bg-primary)',
             borderColor: 'var(--border-light)',
             boxShadow: 'var(--shadow-medium)'
           }}>
        {/* Sidebar Header */}
        <div className="h-16 flex items-center px-6 border-b"
             style={{ borderColor: 'var(--border-light)' }}>
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 rounded-full flex items-center justify-center"
                 style={{ backgroundColor: 'var(--primary)' }}>
              <Heart className="w-4 h-4" style={{ color: 'var(--bg-primary)' }} />
            </div>
            <h2 className="text-xl font-bold tracking-tight leading-tight" style={{ color: 'var(--text-primary)' }}>
              Mission Fresh
            </h2>
          </div>
        </div>

        {/* Navigation Menu */}
        <nav className="p-4">
          <div className="space-y-2">
            <button
              onClick={() => setActiveTab('overview')}
              className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors"
              style={{
                backgroundColor: activeTab === 'overview' ? 'var(--primary)' : 'transparent',
                color: activeTab === 'overview' ? 'var(--bg-primary)' : 'var(--text-primary)'
              }}>
              <User className="w-5 h-5" />
              <span className="font-semibold text-sm tracking-wide">Overview</span>
            </button>
            <button
              onClick={() => setActiveTab('appointments')}
              className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors"
              style={{
                backgroundColor: activeTab === 'appointments' ? 'var(--primary)' : 'transparent',
                color: activeTab === 'appointments' ? 'var(--bg-primary)' : 'var(--text-primary)'
              }}
              onMouseEnter={(e) => {
                if (activeTab !== 'appointments') {
                  e.currentTarget.style.backgroundColor = 'var(--bg-secondary)';
                }
              }}
              onMouseLeave={(e) => {
                if (activeTab !== 'appointments') {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }
              }}>
              <Calendar className="w-5 h-5" />
              <span className="font-semibold text-sm tracking-wide">Appointments</span>
            </button>
            <button
              onClick={() => setActiveTab('messages')}
              className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors"
              style={{
                backgroundColor: activeTab === 'messages' ? 'var(--primary)' : 'transparent',
                color: activeTab === 'messages' ? 'var(--bg-primary)' : 'var(--text-primary)'
              }}
              onMouseEnter={(e) => {
                if (activeTab !== 'messages') {
                  e.currentTarget.style.backgroundColor = 'var(--bg-secondary)';
                }
              }}
              onMouseLeave={(e) => {
                if (activeTab !== 'messages') {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }
              }}>
              <MessageSquare className="w-5 h-5" />
              <span className="font-semibold text-sm tracking-wide">Messages</span>
            </button>
            <button
              onClick={() => setActiveTab('care-groups')}
              className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors"
              style={{
                backgroundColor: activeTab === 'care-groups' ? 'var(--primary)' : 'transparent',
                color: activeTab === 'care-groups' ? 'var(--bg-primary)' : 'var(--text-primary)'
              }}
              onMouseEnter={(e) => {
                if (activeTab !== 'care-groups') {
                  e.currentTarget.style.backgroundColor = 'var(--bg-secondary)';
                }
              }}
              onMouseLeave={(e) => {
                if (activeTab !== 'care-groups') {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }
              }}>
              <Heart className="w-5 h-5" />
              <span className="font-semibold text-sm tracking-wide">Care Groups</span>
            </button>
            <button
              onClick={() => setActiveTab('notifications')}
              className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors"
              style={{
                backgroundColor: activeTab === 'notifications' ? 'var(--primary)' : 'transparent',
                color: activeTab === 'notifications' ? 'var(--bg-primary)' : 'var(--text-primary)'
              }}
              onMouseEnter={(e) => {
                if (activeTab !== 'notifications') {
                  e.currentTarget.style.backgroundColor = 'var(--bg-secondary)';
                }
              }}
              onMouseLeave={(e) => {
                if (activeTab !== 'notifications') {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }
              }}>
              <Bell className="w-5 h-5" />
              <span className="font-semibold text-sm tracking-wide">Notifications</span>
            </button>
            <button
              onClick={() => setActiveTab('settings')}
              className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors"
              style={{
                backgroundColor: activeTab === 'settings' ? 'var(--primary)' : 'transparent',
                color: activeTab === 'settings' ? 'var(--bg-primary)' : 'var(--text-primary)'
              }}
              onMouseEnter={(e) => {
                if (activeTab !== 'settings') {
                  e.currentTarget.style.backgroundColor = 'var(--bg-secondary)';
                }
              }}
              onMouseLeave={(e) => {
                if (activeTab !== 'settings') {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }
              }}>
              <Settings className="w-5 h-5" />
              <span className="font-semibold text-sm tracking-wide">Settings</span>
            </button>
          </div>
        </nav>

        {/* User Profile Section */}
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t"
             style={{ borderColor: 'var(--border-light)' }}>
          <div className="flex items-center space-x-3 mb-3">
            <div className="w-10 h-10 rounded-full flex items-center justify-center"
                 style={{ backgroundColor: 'var(--bg-secondary)' }}>
              <User className="w-5 h-5" style={{ color: 'var(--text-primary)' }} />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-semibold truncate leading-tight" style={{ color: 'var(--text-primary)' }}>
                {user?.first_name} {user?.last_name}
              </p>
              <p className="text-xs font-medium truncate leading-tight" style={{ color: 'var(--text-secondary)' }}>
                {user?.email}
              </p>
            </div>
          </div>
          <button
            onClick={handleSignOut}
            className="w-full px-3 py-2 rounded-lg font-medium text-sm transition-colors border"
            style={{
              borderColor: 'var(--border-medium)',
              color: 'var(--text-primary)',
              backgroundColor: 'transparent'
            }}
          >
            Sign Out
          </button>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 overflow-auto">
        {/* Header */}
        <div className="flex items-center justify-between px-4 py-4 sm:px-6 sm:py-6 lg:px-10 lg:py-8 border-b"
             style={{
               backgroundColor: 'var(--bg-primary)',
               borderColor: 'var(--border-light)'
             }}>
          <div className="space-y-3">
            <h1 className="text-2xl sm:text-3xl font-bold tracking-tight leading-tight" style={{ color: 'var(--text-primary)' }}>
              {activeTab === 'overview' && 'Overview'}
              {activeTab === 'appointments' && 'Appointments'}
              {activeTab === 'messages' && 'Messages'}
              {activeTab === 'care-groups' && 'Care Groups'}
              {activeTab === 'notifications' && 'Notifications'}
              {activeTab === 'settings' && 'Settings'}
            </h1>
            <p className="text-base font-medium leading-relaxed" style={{ color: 'var(--text-secondary)' }}>
              {activeTab === 'overview' && `Welcome back, ${user?.first_name}`}
              {activeTab === 'appointments' && 'Manage your appointments and bookings'}
              {activeTab === 'messages' && 'View and send messages'}
              {activeTab === 'care-groups' && 'Connect with your care community'}
              {activeTab === 'notifications' && 'Stay updated with important alerts'}
              {activeTab === 'settings' && 'Customize your account preferences'}
            </p>
          </div>
        </div>

        {/* Dashboard Content */}
        <div className="p-4 sm:p-6 lg:p-10">
          {activeTab === 'overview' && (
            <>
              {loading ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 lg:gap-8 mb-8 sm:mb-10 lg:mb-12">
                  {[1, 2, 3, 4].map((i) => (
                    <div key={i} className="p-6 rounded-2xl border animate-pulse"
                         style={{
                           backgroundColor: 'var(--bg-primary)',
                           borderColor: 'var(--border-light)'
                         }}>
                      <div className="flex items-center justify-between mb-4">
                        <div className="w-12 h-12 rounded-full" style={{ backgroundColor: 'var(--border-light)' }}></div>
                        <div className="w-2 h-2 rounded-full" style={{ backgroundColor: 'var(--border-light)' }}></div>
                      </div>
                      <div className="space-y-2">
                        <div className="h-4 rounded" style={{ backgroundColor: 'var(--border-light)' }}></div>
                        <div className="h-12 rounded" style={{ backgroundColor: 'var(--border-light)' }}></div>
                        <div className="h-4 rounded w-3/4" style={{ backgroundColor: 'var(--border-light)' }}></div>
                      </div>
                      <div className="mt-4 pt-4 border-t" style={{ borderColor: 'var(--border-light)' }}>
                        <div className="h-10 rounded-xl" style={{ backgroundColor: 'var(--border-light)' }}></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : error ? (
                <div className="text-center py-12">
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center"
                       style={{ backgroundColor: 'var(--error)', color: 'var(--bg-primary)' }}>
                    <span className="text-2xl">⚠</span>
                  </div>
                  <h3 className="text-xl font-bold mb-2" style={{ color: 'var(--text-primary)' }}>
                    Unable to Load Dashboard
                  </h3>
                  <p className="text-base mb-4" style={{ color: 'var(--text-secondary)' }}>
                    {error}
                  </p>
                  <button
                    className="px-6 py-3 rounded-xl font-bold text-sm transition-all duration-250"
                    style={{
                      backgroundColor: 'var(--primary)',
                      color: 'var(--bg-primary)'
                    }}
                    onClick={() => window.location.reload()}>
                    Retry
                  </button>
                </div>
              ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 lg:gap-8 mb-8 sm:mb-10 lg:mb-12">
            <div className="group p-6 rounded-2xl border cursor-pointer transition-all duration-300 hover:scale-105 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2"
                 style={{
                   backgroundColor: 'var(--bg-primary)',
                   borderColor: 'var(--border-light)',
                   boxShadow: 'var(--shadow-card)',
                   focusRingColor: 'var(--primary)'
                 }}
                 role="button"
                 tabIndex={0}
                 aria-label="Upcoming appointments dashboard card"
                 onMouseEnter={(e) => {
                   e.currentTarget.style.boxShadow = 'var(--shadow-card-hover)';
                   e.currentTarget.style.transform = 'scale(1.05) translateY(-4px)';
                 }}
                 onMouseLeave={(e) => {
                   e.currentTarget.style.boxShadow = 'var(--shadow-card)';
                   e.currentTarget.style.transform = 'scale(1) translateY(0)';
                 }}
                 onKeyDown={(e) => {
                   if (e.key === 'Enter' || e.key === ' ') {
                     e.preventDefault();
                     // Handle card activation
                   }
                 }}>
              <div className="flex items-center justify-between mb-6">
                <div className="w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300 group-hover:scale-110"
                     style={{
                       backgroundColor: 'var(--primary)',
                       boxShadow: 'var(--shadow-icon)'
                     }}>
                  <Calendar className="w-6 h-6" style={{ color: 'var(--bg-primary)' }} />
                </div>
                <div className="text-right">
                  <div className="w-2 h-2 rounded-full" style={{ backgroundColor: 'var(--primary)' }}></div>
                </div>
              </div>
              <div className="space-y-2">
                <h3 className="text-sm font-semibold tracking-wide uppercase"
                    style={{ color: 'var(--text-secondary)', letterSpacing: '0.05em' }}>
                  Upcoming Appointments
                </h3>
                <div className="flex items-center space-x-4 mb-3">
                  <p className="text-5xl font-black tracking-tighter leading-none" style={{ color: 'var(--text-primary)' }}>
                    {dashboardStats.upcomingAppointments}
                  </p>
                  <div className="flex-1">
                    <div className="w-full h-2 rounded-full" style={{ backgroundColor: 'var(--border-light)' }}>
                      <div className="h-2 rounded-full transition-all duration-500"
                           style={{
                             backgroundColor: 'var(--primary)',
                             width: `${memoizedStats.appointmentProgress}%`
                           }}></div>
                    </div>
                    <div className="text-xs font-medium mt-1" style={{ color: 'var(--text-secondary)' }}>
                      {dashboardStats.upcomingAppointments}/10 capacity
                    </div>
                  </div>
                </div>
                <p className="text-sm font-semibold leading-tight" style={{ color: 'var(--text-secondary)' }}>
                  {dashboardStats.upcomingAppointments === 0 ? 'No appointments scheduled' :
                   dashboardStats.upcomingAppointments === 1 ? 'Next appointment soon' : 'Multiple appointments'}
                </p>
              </div>
              <div className="mt-4 pt-4 border-t" style={{ borderColor: 'var(--border-light)' }}>
                <button
                  className="w-full px-6 py-3 rounded-xl font-bold text-sm tracking-wide transition-all duration-250 hover:scale-102 focus:scale-102 focus:outline-none focus:ring-2 focus:ring-offset-2"
                  style={{
                    backgroundColor: 'var(--primary)',
                    color: 'var(--bg-primary)',
                    boxShadow: 'var(--shadow-medium)',
                    letterSpacing: '0.025em'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.boxShadow = 'var(--shadow-large)';
                    e.currentTarget.style.transform = 'scale(1.02)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.boxShadow = 'var(--shadow-medium)';
                    e.currentTarget.style.transform = 'scale(1)';
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.boxShadow = 'var(--shadow-large)';
                    e.currentTarget.style.outline = `2px solid var(--primary)`;
                    e.currentTarget.style.outlineOffset = '2px';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.boxShadow = 'var(--shadow-medium)';
                    e.currentTarget.style.outline = 'none';
                  }}>
                  Schedule New Appointment
                </button>
              </div>
            </div>

            <div className="group p-6 rounded-2xl border cursor-pointer transition-all duration-300 hover:scale-105 hover:-translate-y-1"
                 style={{
                   backgroundColor: 'var(--bg-primary)',
                   borderColor: 'var(--border-light)',
                   boxShadow: 'var(--shadow-card)'
                 }}
                 onMouseEnter={(e) => {
                   e.currentTarget.style.boxShadow = 'var(--shadow-card-hover)';
                   e.currentTarget.style.transform = 'scale(1.05) translateY(-4px)';
                 }}
                 onMouseLeave={(e) => {
                   e.currentTarget.style.boxShadow = 'var(--shadow-card)';
                   e.currentTarget.style.transform = 'scale(1) translateY(0)';
                 }}>
              <div className="flex items-center justify-between mb-6">
                <div className="w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300 group-hover:scale-110"
                     style={{
                       backgroundColor: 'var(--primary)',
                       boxShadow: 'var(--shadow-icon)'
                     }}>
                  <MessageSquare className="w-6 h-6" style={{ color: 'var(--bg-primary)' }} />
                </div>
                <div className="text-right">
                  <div className="w-2 h-2 rounded-full" style={{ backgroundColor: 'var(--primary)' }}></div>
                </div>
              </div>
              <div className="space-y-2">
                <h3 className="text-sm font-semibold tracking-wide uppercase"
                    style={{ color: 'var(--text-secondary)', letterSpacing: '0.05em' }}>
                  Unread Messages
                </h3>
                <div className="flex items-center space-x-4 mb-3">
                  <p className="text-5xl font-black tracking-tighter leading-none" style={{ color: 'var(--text-primary)' }}>
                    {dashboardStats.unreadMessages}
                  </p>
                  <div className="flex-1">
                    <div className="w-full h-2 rounded-full" style={{ backgroundColor: 'var(--border-light)' }}>
                      <div className="h-2 rounded-full transition-all duration-500"
                           style={{
                             backgroundColor: memoizedStats.messagePriority === 'high' ? 'var(--error)' : 'var(--primary)',
                             width: `${memoizedStats.messageProgress}%`
                           }}></div>
                    </div>
                    <div className="text-xs font-medium mt-1" style={{ color: 'var(--text-secondary)' }}>
                      {memoizedStats.messagePriority === 'high' ? 'High priority' : 'Normal'}
                    </div>
                  </div>
                </div>
                <p className="text-sm font-semibold leading-tight" style={{ color: 'var(--text-secondary)' }}>
                  {dashboardStats.unreadMessages === 0 ? 'All messages read' :
                   dashboardStats.unreadMessages === 1 ? 'New message waiting' : 'Multiple new messages'}
                </p>
              </div>
              <div className="mt-4 pt-4 border-t" style={{ borderColor: 'var(--border-light)' }}>
                <button
                  className="w-full px-6 py-3 rounded-xl font-bold text-sm tracking-wide transition-all duration-250 hover:scale-102 focus:scale-102 focus:outline-none focus:ring-2 focus:ring-offset-2"
                  style={{
                    backgroundColor: 'var(--primary)',
                    color: 'var(--bg-primary)',
                    boxShadow: 'var(--shadow-medium)',
                    letterSpacing: '0.025em'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.boxShadow = 'var(--shadow-large)';
                    e.currentTarget.style.transform = 'scale(1.02)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.boxShadow = 'var(--shadow-medium)';
                    e.currentTarget.style.transform = 'scale(1)';
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.boxShadow = 'var(--shadow-large)';
                    e.currentTarget.style.outline = `2px solid var(--primary)`;
                    e.currentTarget.style.outlineOffset = '2px';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.boxShadow = 'var(--shadow-medium)';
                    e.currentTarget.style.outline = 'none';
                  }}>
                  Compose New Message
                </button>
              </div>
            </div>

            <div className="group p-6 rounded-2xl border cursor-pointer transition-all duration-300 hover:scale-105 hover:-translate-y-1"
                 style={{
                   backgroundColor: 'var(--bg-primary)',
                   borderColor: 'var(--border-light)',
                   boxShadow: 'var(--shadow-card)'
                 }}
                 onMouseEnter={(e) => {
                   e.currentTarget.style.boxShadow = 'var(--shadow-card-hover)';
                   e.currentTarget.style.transform = 'scale(1.05) translateY(-4px)';
                 }}
                 onMouseLeave={(e) => {
                   e.currentTarget.style.boxShadow = 'var(--shadow-card)';
                   e.currentTarget.style.transform = 'scale(1) translateY(0)';
                 }}>
              <div className="flex items-center justify-between mb-6">
                <div className="w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300 group-hover:scale-110"
                     style={{
                       backgroundColor: 'var(--primary)',
                       boxShadow: 'var(--shadow-icon)'
                     }}>
                  <Heart className="w-6 h-6" style={{ color: 'var(--bg-primary)' }} />
                </div>
                <div className="text-right">
                  <div className="w-2 h-2 rounded-full" style={{ backgroundColor: 'var(--primary)' }}></div>
                </div>
              </div>
              <div className="space-y-2">
                <h3 className="text-sm font-semibold tracking-wide uppercase"
                    style={{ color: 'var(--text-secondary)', letterSpacing: '0.05em' }}>
                  Care Groups
                </h3>
                <p className="text-5xl font-black tracking-tighter leading-none" style={{ color: 'var(--text-primary)' }}>
                  {dashboardStats.careGroupsCount}
                </p>
                <p className="text-sm font-semibold leading-tight" style={{ color: 'var(--text-secondary)' }}>
                  {dashboardStats.careGroupsCount === 0 ? 'Join your first group' :
                   dashboardStats.careGroupsCount === 1 ? 'Active in one group' : 'Multiple groups joined'}
                </p>
              </div>
              <div className="mt-4 pt-4 border-t" style={{ borderColor: 'var(--border-light)' }}>
                <button
                  className="w-full px-6 py-3 rounded-xl font-bold text-sm tracking-wide transition-all duration-250 hover:scale-102 focus:scale-102 focus:outline-none focus:ring-2 focus:ring-offset-2"
                  style={{
                    backgroundColor: 'var(--primary)',
                    color: 'var(--bg-primary)',
                    boxShadow: 'var(--shadow-medium)',
                    letterSpacing: '0.025em'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.boxShadow = 'var(--shadow-large)';
                    e.currentTarget.style.transform = 'scale(1.02)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.boxShadow = 'var(--shadow-medium)';
                    e.currentTarget.style.transform = 'scale(1)';
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.boxShadow = 'var(--shadow-large)';
                    e.currentTarget.style.outline = `2px solid var(--primary)`;
                    e.currentTarget.style.outlineOffset = '2px';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.boxShadow = 'var(--shadow-medium)';
                    e.currentTarget.style.outline = 'none';
                  }}>
                  {dashboardStats.careGroupsCount === 0 ? 'Join Care Group' : 'Browse Groups'}
                </button>
              </div>
            </div>

            <div className="group p-6 rounded-2xl border cursor-pointer transition-all duration-300 hover:scale-105 hover:-translate-y-1"
                 style={{
                   backgroundColor: 'var(--bg-primary)',
                   borderColor: 'var(--border-light)',
                   boxShadow: 'var(--shadow-card)'
                 }}
                 onMouseEnter={(e) => {
                   e.currentTarget.style.boxShadow = 'var(--shadow-card-hover)';
                   e.currentTarget.style.transform = 'scale(1.05) translateY(-4px)';
                 }}
                 onMouseLeave={(e) => {
                   e.currentTarget.style.boxShadow = 'var(--shadow-card)';
                   e.currentTarget.style.transform = 'scale(1) translateY(0)';
                 }}>
              <div className="flex items-center justify-between mb-6">
                <div className="w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300 group-hover:scale-110"
                     style={{
                       backgroundColor: 'var(--primary)',
                       boxShadow: 'var(--shadow-icon)'
                     }}>
                  <User className="w-6 h-6" style={{ color: 'var(--bg-primary)' }} />
                </div>
                <div className="text-right">
                  <div className="w-2 h-2 rounded-full" style={{ backgroundColor: 'var(--primary)' }}></div>
                </div>
              </div>
              <div className="space-y-2">
                <h3 className="text-sm font-semibold tracking-wide uppercase"
                    style={{ color: 'var(--text-secondary)', letterSpacing: '0.05em' }}>
                  Saved Providers
                </h3>
                <p className="text-5xl font-black tracking-tighter leading-none" style={{ color: 'var(--text-primary)' }}>
                  {dashboardStats.savedProviders}
                </p>
                <p className="text-sm font-semibold leading-tight" style={{ color: 'var(--text-secondary)' }}>
                  {dashboardStats.savedProviders === 0 ? 'No providers saved yet' :
                   dashboardStats.savedProviders === 1 ? 'One provider saved' : 'Multiple providers saved'}
                </p>
              </div>
              <div className="mt-4 pt-4 border-t" style={{ borderColor: 'var(--border-light)' }}>
                <button
                  className="w-full px-6 py-3 rounded-xl font-bold text-sm tracking-wide transition-all duration-250 hover:scale-102 focus:scale-102 focus:outline-none focus:ring-2 focus:ring-offset-2"
                  style={{
                    backgroundColor: 'var(--primary)',
                    color: 'var(--bg-primary)',
                    boxShadow: 'var(--shadow-medium)',
                    letterSpacing: '0.025em'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.boxShadow = 'var(--shadow-large)';
                    e.currentTarget.style.transform = 'scale(1.02)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.boxShadow = 'var(--shadow-medium)';
                    e.currentTarget.style.transform = 'scale(1)';
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.boxShadow = 'var(--shadow-large)';
                    e.currentTarget.style.outline = `2px solid var(--primary)`;
                    e.currentTarget.style.outlineOffset = '2px';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.boxShadow = 'var(--shadow-medium)';
                    e.currentTarget.style.outline = 'none';
                  }}>
                  {dashboardStats.savedProviders === 0 ? 'Find Providers' : 'View Providers'}
                </button>
              </div>
            </div>
          </div>

          {/* Recent Activity Section */}
          <div className="mt-8 sm:mt-10 lg:mt-12">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 sm:mb-8 space-y-4 sm:space-y-0">
              <h2 className="text-xl sm:text-2xl font-bold tracking-tight leading-tight" style={{ color: 'var(--text-primary)' }}>
                Recent Activity
              </h2>
              <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search activity..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full sm:w-64 px-4 py-2 rounded-xl border text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2"
                    style={{
                      backgroundColor: 'var(--bg-primary)',
                      borderColor: 'var(--border-light)',
                      color: 'var(--text-primary)',
                      focusRingColor: 'var(--primary)'
                    }}
                    aria-label="Search recent activity"
                    role="searchbox"
                    aria-describedby="search-help"
                  />
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <svg className="w-4 h-4" style={{ color: 'var(--text-secondary)' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                </div>
                <select
                  value={activityFilter}
                  onChange={(e) => setActivityFilter(e.target.value)}
                  className="px-4 py-2 rounded-xl border text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    borderColor: 'var(--border-light)',
                    color: 'var(--text-primary)',
                    focusRingColor: 'var(--primary)'
                  }}
                  aria-label="Filter activity by type"
                  id="activity-filter">
                  <option value="all">All Activity</option>
                  <option value="appointments">Appointments</option>
                  <option value="messages">Messages</option>
                  <option value="health">Health Updates</option>
                </select>
              </div>
            </div>
            <div className="space-y-4 sm:space-y-6">
              {loading ? (
                <>
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="flex items-center space-x-4 sm:space-x-6 p-4 sm:p-6 rounded-xl border animate-pulse"
                         style={{
                           backgroundColor: 'var(--bg-primary)',
                           borderColor: 'var(--border-light)'
                         }}>
                      <div className="w-12 h-12 rounded-full" style={{ backgroundColor: 'var(--border-light)' }}></div>
                      <div className="flex-1 space-y-2">
                        <div className="h-4 rounded w-3/4" style={{ backgroundColor: 'var(--border-light)' }}></div>
                        <div className="h-3 rounded w-1/2" style={{ backgroundColor: 'var(--border-light)' }}></div>
                      </div>
                      <div className="w-16 h-3 rounded" style={{ backgroundColor: 'var(--border-light)' }}></div>
                    </div>
                  ))}
                </>
              ) : error ? (
                <div className="text-center py-8">
                  <p className="text-base" style={{ color: 'var(--text-secondary)' }}>
                    Unable to load recent activity
                  </p>
                </div>
              ) : (
                <>
              {/* Activity Item 1 */}
              <div className="flex items-center space-x-4 sm:space-x-6 p-4 sm:p-6 rounded-xl border transition-all duration-300 hover:scale-102 hover:-translate-y-1 cursor-pointer"
                   style={{
                     backgroundColor: 'var(--bg-primary)',
                     borderColor: 'var(--border-light)',
                     boxShadow: 'var(--shadow-light)'
                   }}
                   onMouseEnter={(e) => {
                     e.currentTarget.style.boxShadow = 'var(--shadow-card)';
                     e.currentTarget.style.transform = 'scale(1.02) translateY(-2px)';
                   }}
                   onMouseLeave={(e) => {
                     e.currentTarget.style.boxShadow = 'var(--shadow-light)';
                     e.currentTarget.style.transform = 'scale(1) translateY(0)';
                   }}>
                <div className="w-12 h-12 rounded-full flex items-center justify-center"
                     style={{ backgroundColor: 'var(--primary)' }}>
                  <Calendar className="w-6 h-6" style={{ color: 'var(--bg-primary)' }} />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-semibold" style={{ color: 'var(--text-primary)' }}>
                    Appointment Reminder
                  </p>
                  <p className="text-xs font-medium" style={{ color: 'var(--text-secondary)' }}>
                    Your next appointment is in 2 days
                  </p>
                </div>
                <div className="text-xs font-medium" style={{ color: 'var(--text-secondary)' }}>
                  2 hours ago
                </div>
              </div>

              {/* Activity Item 2 */}
              <div className="flex items-center space-x-6 p-6 rounded-xl border transition-all duration-200 hover:scale-102"
                   style={{
                     backgroundColor: 'var(--bg-primary)',
                     borderColor: 'var(--border-light)',
                     boxShadow: 'var(--shadow-light)'
                   }}>
                <div className="w-12 h-12 rounded-full flex items-center justify-center"
                     style={{ backgroundColor: 'var(--primary)' }}>
                  <Heart className="w-6 h-6" style={{ color: 'var(--bg-primary)' }} />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-semibold" style={{ color: 'var(--text-primary)' }}>
                    Progress Milestone
                  </p>
                  <p className="text-xs font-medium" style={{ color: 'var(--text-secondary)' }}>
                    Congratulations! You've been smoke-free for 7 days
                  </p>
                </div>
                <div className="text-xs font-medium" style={{ color: 'var(--text-secondary)' }}>
                  1 day ago
                </div>
              </div>

              {/* Activity Item 3 */}
              <div className="flex items-center space-x-6 p-6 rounded-xl border transition-all duration-200 hover:scale-102"
                   style={{
                     backgroundColor: 'var(--bg-primary)',
                     borderColor: 'var(--border-light)',
                     boxShadow: 'var(--shadow-light)'
                   }}>
                <div className="w-12 h-12 rounded-full flex items-center justify-center"
                     style={{ backgroundColor: 'var(--primary)' }}>
                  <MessageSquare className="w-6 h-6" style={{ color: 'var(--bg-primary)' }} />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-semibold" style={{ color: 'var(--text-primary)' }}>
                    New Message
                  </p>
                  <p className="text-xs font-medium" style={{ color: 'var(--text-secondary)' }}>
                    Dr. Smith sent you a message about your progress
                  </p>
                </div>
                <div className="text-xs font-medium" style={{ color: 'var(--text-secondary)' }}>
                  3 days ago
                </div>
              </div>
                </>
              )}
            </div>
          </div>
        </div>
              )}
            </>
          )}

      {activeTab === 'appointments' && (
        <div className="space-y-6">
          <div className="text-center py-12">
            <Calendar className="w-16 h-16 mx-auto mb-4" style={{ color: 'var(--text-secondary)' }} />
            <h3 className="text-2xl font-bold tracking-tight leading-tight mb-3" style={{ color: 'var(--text-primary)' }}>
              No Appointments Yet
            </h3>
            <p className="text-base font-medium leading-relaxed" style={{ color: 'var(--text-secondary)' }}>
              Your upcoming appointments will appear here
            </p>
          </div>
        </div>
      )}

      {activeTab === 'messages' && (
            <div className="space-y-6">
              <div className="text-center py-12">
                <MessageSquare className="w-16 h-16 mx-auto mb-4" style={{ color: 'var(--text-secondary)' }} />
                <h3 className="text-2xl font-bold tracking-tight leading-tight mb-3" style={{ color: 'var(--text-primary)' }}>
                  No Messages Yet
                </h3>
                <p className="text-base font-medium leading-relaxed" style={{ color: 'var(--text-secondary)' }}>
                  Your messages will appear here
                </p>
              </div>
            </div>
          )}

          {activeTab === 'care-groups' && (
            <div className="space-y-6">
              <div className="text-center py-12">
                <Heart className="w-16 h-16 mx-auto mb-4" style={{ color: 'var(--text-secondary)' }} />
                <h3 className="text-2xl font-bold tracking-tight leading-tight mb-3" style={{ color: 'var(--text-primary)' }}>
                  No Care Groups Yet
                </h3>
                <p className="text-base font-medium leading-relaxed" style={{ color: 'var(--text-secondary)' }}>
                  Join or create care groups to connect with your community
                </p>
              </div>
            </div>
          )}

          {activeTab === 'notifications' && (
            <div className="space-y-6">
              <div className="text-center py-12">
                <Bell className="w-16 h-16 mx-auto mb-4" style={{ color: 'var(--text-secondary)' }} />
                <h3 className="text-2xl font-bold tracking-tight leading-tight mb-3" style={{ color: 'var(--text-primary)' }}>
                  No Notifications
                </h3>
                <p className="text-base font-medium leading-relaxed" style={{ color: 'var(--text-secondary)' }}>
                  You're all caught up! Notifications will appear here
                </p>
              </div>
            </div>
          )}

          {activeTab === 'settings' && (
            <div className="space-y-6">
              <div className="text-center py-12">
                <Settings className="w-16 h-16 mx-auto mb-4" style={{ color: 'var(--text-secondary)' }} />
                <h3 className="text-2xl font-bold tracking-tight leading-tight mb-3" style={{ color: 'var(--text-primary)' }}>
                  Settings
                </h3>
                <p className="text-base font-medium leading-relaxed" style={{ color: 'var(--text-secondary)' }}>
                  Customize your account preferences and settings
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
