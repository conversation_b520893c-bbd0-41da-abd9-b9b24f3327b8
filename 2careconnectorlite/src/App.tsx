import React from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import Header from './components/Header'

// Page Imports
import Home from './pages/Home'
import Caregivers from './pages/Caregivers'
import Companions from './pages/Companions'
import Professionals from './pages/Professionals'
import CareCheckers from './pages/CareCheckers'
import CareGroups from './pages/CareGroups'
import JoinGroup from './pages/JoinGroup'
import Dashboard from './pages/Dashboard'
import Auth from './pages/Auth'
import GetStarted from './pages/GetStarted'
import HowItWorks from './pages/HowItWorks'
import Features from './pages/Features'
import Products from './pages/Products'
import ProviderProfile from './pages/ProviderProfile'
import BookingConfirmationPage from './pages/BookingConfirmationPage'
import CreateBookingPage from './pages/CreateBookingPage'
import MyBookingsPage from './pages/MyBookingsPage'
import BookingDetailPage from './pages/BookingDetailPage'
import BookingNotificationsPage from './pages/BookingNotificationsPage'
import ProfileEdit from './pages/ProfileEdit'
import MessagingSystem from './pages/MessagingSystem'
import AIAssistant from './pages/AIAssistant'

import './index.css'

function App() {
  return (
    <Router>
      <div style={{ minHeight: '100vh', backgroundColor: 'var(--bg-primary)' }}>
        <Header />
        <main>
          <Routes>
            {/* Home and Main Pages */}
            <Route path="/" element={<Home />} />
            <Route path="/home" element={<Home />} />
            <Route path="/dashboard" element={<Dashboard />} />
            
            {/* Find Care Routes */}
            <Route path="/caregivers" element={<Caregivers />} />
            <Route path="/companions" element={<Companions />} />
            <Route path="/professionals" element={<Professionals />} />
            <Route path="/care-checkers" element={<CareCheckers />} />
            
            {/* Care Groups Routes */}
            <Route path="/care-groups" element={<CareGroups />} />
            <Route path="/browse-groups" element={<CareGroups />} />
            <Route path="/create-group" element={<CareGroups />} />
            <Route path="/join-group" element={<JoinGroup />} />
            
            {/* Authentication Routes */}
            <Route path="/sign-in" element={<Auth />} />
            <Route path="/sign-up" element={<Auth />} />
            <Route path="/auth" element={<Auth />} />
            
            {/* Information Pages */}
            <Route path="/get-started" element={<GetStarted />} />
            <Route path="/how-it-works" element={<HowItWorks />} />
            <Route path="/features" element={<Features />} />
            <Route path="/products" element={<Products />} />
            
            {/* Provider and Booking Routes */}
            <Route path="/provider/:providerType/:providerId" element={<ProviderProfile />} />
            <Route path="/booking/create" element={<CreateBookingPage />} />
            <Route path="/booking/confirmation" element={<BookingConfirmationPage />} />
            <Route path="/booking/notifications" element={<BookingNotificationsPage />} />
            <Route path="/my-bookings" element={<MyBookingsPage />} />
            <Route path="/booking/:id" element={<BookingDetailPage />} />
            
            {/* User Account Routes */}
            <Route path="/profile" element={<ProfileEdit />} />
            <Route path="/profile/edit" element={<ProfileEdit />} />
            
            {/* Communication Routes */}
            <Route path="/messages" element={<MessagingSystem />} />
            <Route path="/ai-assistant" element={<AIAssistant />} />
          </Routes>
        </main>
      </div>
    </Router>
  )
}

export default App
